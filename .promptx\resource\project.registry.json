{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T14:03:14.304Z", "updatedAt": "2025-07-29T14:03:14.357Z", "resourceCount": 24}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-29T14:03:14.310Z", "updatedAt": "2025-07-29T14:03:14.310Z", "scannedAt": "2025-07-29T14:03:14.310Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.312Z", "updatedAt": "2025-07-29T14:03:14.312Z", "scannedAt": "2025-07-29T14:03:14.312Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.313Z", "updatedAt": "2025-07-29T14:03:14.313Z", "scannedAt": "2025-07-29T14:03:14.313Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-29T14:03:14.315Z", "updatedAt": "2025-07-29T14:03:14.315Z", "scannedAt": "2025-07-29T14:03:14.315Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.316Z", "updatedAt": "2025-07-29T14:03:14.316Z", "scannedAt": "2025-07-29T14:03:14.316Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.318Z", "updatedAt": "2025-07-29T14:03:14.318Z", "scannedAt": "2025-07-29T14:03:14.318Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-29T14:03:14.320Z", "updatedAt": "2025-07-29T14:03:14.320Z", "scannedAt": "2025-07-29T14:03:14.320Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.323Z", "updatedAt": "2025-07-29T14:03:14.323Z", "scannedAt": "2025-07-29T14:03:14.323Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.325Z", "updatedAt": "2025-07-29T14:03:14.325Z", "scannedAt": "2025-07-29T14:03:14.325Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-29T14:03:14.327Z", "updatedAt": "2025-07-29T14:03:14.327Z", "scannedAt": "2025-07-29T14:03:14.327Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.329Z", "updatedAt": "2025-07-29T14:03:14.329Z", "scannedAt": "2025-07-29T14:03:14.329Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.331Z", "updatedAt": "2025-07-29T14:03:14.331Z", "scannedAt": "2025-07-29T14:03:14.331Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.333Z", "updatedAt": "2025-07-29T14:03:14.333Z", "scannedAt": "2025-07-29T14:03:14.333Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-29T14:03:14.334Z", "updatedAt": "2025-07-29T14:03:14.334Z", "scannedAt": "2025-07-29T14:03:14.334Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.336Z", "updatedAt": "2025-07-29T14:03:14.336Z", "scannedAt": "2025-07-29T14:03:14.336Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.341Z", "updatedAt": "2025-07-29T14:03:14.341Z", "scannedAt": "2025-07-29T14:03:14.341Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.343Z", "updatedAt": "2025-07-29T14:03:14.343Z", "scannedAt": "2025-07-29T14:03:14.343Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.344Z", "updatedAt": "2025-07-29T14:03:14.344Z", "scannedAt": "2025-07-29T14:03:14.344Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-29T14:03:14.346Z", "updatedAt": "2025-07-29T14:03:14.346Z", "scannedAt": "2025-07-29T14:03:14.346Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-29T14:03:14.347Z", "updatedAt": "2025-07-29T14:03:14.347Z", "scannedAt": "2025-07-29T14:03:14.347Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.349Z", "updatedAt": "2025-07-29T14:03:14.349Z", "scannedAt": "2025-07-29T14:03:14.349Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.351Z", "updatedAt": "2025-07-29T14:03:14.351Z", "scannedAt": "2025-07-29T14:03:14.351Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.352Z", "updatedAt": "2025-07-29T14:03:14.352Z", "scannedAt": "2025-07-29T14:03:14.352Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-29T14:03:14.354Z", "updatedAt": "2025-07-29T14:03:14.354Z", "scannedAt": "2025-07-29T14:03:14.354Z", "path": "role/system-director/thought/team-coordination.thought.md"}}], "stats": {"totalResources": 24, "byProtocol": {"role": 6, "execution": 9, "thought": 9}, "bySource": {"project": 24}}}